# ActivityChronos Magisk Module

## Installation

1. 编译Rust程序：`cargo build --release --target aarch64-linux-android`
2. 复制二进制文件到：`magisk_module/system/bin/activity-chronos`
3. 打包模块：`cd magisk_module && zip -r ActivityChronos.zip *`
4. 在Magisk Manager中安装zip文件

## 文件说明

- `module.prop`: 模块配置信息
- `service.sh`: 开机启动脚本
- `system/bin/activity-chronos`: 程序二进制文件位置

## 日志位置

程序日志存储在：`/data/local/tmp/activity_chronos.log`
