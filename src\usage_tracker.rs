use std::collections::HashMap;
use std::fs;
use std::path::PathBuf;
use std::time::Duration;
use std::io::Write;
use chrono::{DateTime, Utc};
use serde_json;
use thiserror::Error;
use log::{info, warn, error};

use crate::models::{ActivityUsage, DailyReport, ActivitySummary};

#[derive(Debug, Error)]
pub enum UsageTrackerError {
    #[error("IO error: {0}")]
    IoError(#[from] std::io::Error),
    #[error("JSON error: {0}")]
    JsonError(#[from] serde_json::Error),
    #[error("Invalid data directory: {0}")]
    InvalidDataDir(String),
}

pub struct UsageTracker {
    data_dir: PathBuf,
    daily_cache: HashMap<String, Vec<ActivityUsage>>,
}

impl UsageTracker {
    pub fn new(data_dir: &str) -> Result<Self, UsageTrackerError> {
        let data_dir = PathBuf::from(data_dir);
        
        // Create data directory if it doesn't exist
        if !data_dir.exists() {
            fs::create_dir_all(&data_dir)?;
            info!("Created data directory: {}", data_dir.display());
        }
        
        // Create subdirectories
        let raw_dir = data_dir.join("raw");
        let reports_dir = data_dir.join("reports");
        
        fs::create_dir_all(&raw_dir)?;
        fs::create_dir_all(&reports_dir)?;
        
        Ok(Self {
            data_dir,
            daily_cache: HashMap::new(),
        })
    }
    
    pub async fn record_usage(&mut self, activity_name: &str, duration: Duration) -> Result<(), UsageTrackerError> {
        if duration.as_secs() == 0 {
            return Ok(());
        }
        
        let usage = ActivityUsage::new(activity_name.to_string(), duration);
        let date_key = usage.start_time.format("%Y-%m-%d").to_string();
        
        // Add to cache
        self.daily_cache
            .entry(date_key.clone())
            .or_insert_with(Vec::new)
            .push(usage.clone());
        
        // Write to raw data file
        self.write_raw_usage(&usage).await?;
        
        // Update daily report
        self.update_daily_report(&date_key).await?;
        
        Ok(())
    }
    
    async fn write_raw_usage(&self, usage: &ActivityUsage) -> Result<(), UsageTrackerError> {
        let date_str = usage.start_time.format("%Y-%m-%d").to_string();
        let file_path = self.data_dir.join("raw").join(format!("{}.jsonl", date_str));
        
        let json_line = serde_json::to_string(usage)?;
        let content = format!("{}\n", json_line);
        
        // Append to file
        let mut file = fs::OpenOptions::new()
            .create(true)
            .append(true)
            .open(&file_path)?;
        file.write_all(content.as_bytes())?;
        
        Ok(())
    }
    
    async fn update_daily_report(&mut self, date_key: &str) -> Result<(), UsageTrackerError> {
        let empty_vec = Vec::new();
        let usages = self.daily_cache.get(date_key).unwrap_or(&empty_vec);
        
        // Group by activity and calculate summaries
        let mut activity_summaries: HashMap<String, ActivitySummary> = HashMap::new();
        let mut total_usage_seconds = 0u64;
        
        for usage in usages {
            total_usage_seconds += usage.duration_seconds;
            
            let summary = activity_summaries
                .entry(usage.activity_name.clone())
                .or_insert_with(|| ActivitySummary {
                    activity_name: usage.activity_name.clone(),
                    package_name: usage.package_name.clone(),
                    total_duration_seconds: 0,
                    session_count: 0,
                    first_use: usage.start_time,
                    last_use: usage.start_time,
                });
            
            summary.total_duration_seconds += usage.duration_seconds;
            summary.session_count += 1;
            
            if usage.start_time < summary.first_use {
                summary.first_use = usage.start_time;
            }
            
            if usage.end_time > summary.last_use {
                summary.last_use = usage.end_time;
            }
        }
        
        // Convert to sorted vector
        let mut activities: Vec<ActivitySummary> = activity_summaries.into_values().collect();
        activities.sort_by(|a, b| b.total_duration_seconds.cmp(&a.total_duration_seconds));
        
        let report = DailyReport {
            date: date_key.to_string(),
            total_usage_seconds,
            activities,
        };
        
        // Write report
        let report_path = self.data_dir.join("reports").join(format!("{}.json", date_key));
        let json_content = serde_json::to_string_pretty(&report)?;
        fs::write(&report_path, json_content)?;
        
        info!("Updated daily report for {}: {} activities, {} total seconds", 
               date_key, report.activities.len(), total_usage_seconds);
        
        Ok(())
    }
    
    pub async fn load_daily_data(&mut self, date: &str) -> Result<Vec<ActivityUsage>, UsageTrackerError> {
        // Check cache first
        if let Some(cached) = self.daily_cache.get(date) {
            return Ok(cached.clone());
        }
        
        // Load from file
        let file_path = self.data_dir.join("raw").join(format!("{}.jsonl", date));
        
        if !file_path.exists() {
            return Ok(Vec::new());
        }
        
        let content = fs::read_to_string(&file_path)?;
        let mut usages = Vec::new();
        
        for line in content.lines() {
            if line.trim().is_empty() {
                continue;
            }
            
            match serde_json::from_str::<ActivityUsage>(line) {
                Ok(usage) => usages.push(usage),
                Err(e) => warn!("Failed to parse line in {}: {}", file_path.display(), e),
            }
        }
        
        // Cache the loaded data
        self.daily_cache.insert(date.to_string(), usages.clone());
        
        Ok(usages)
    }
    
    pub async fn get_daily_report(&self, date: &str) -> Result<Option<DailyReport>, UsageTrackerError> {
        let report_path = self.data_dir.join("reports").join(format!("{}.json", date));
        
        if !report_path.exists() {
            return Ok(None);
        }
        
        let content = fs::read_to_string(&report_path)?;
        let report: DailyReport = serde_json::from_str(&content)?;
        
        Ok(Some(report))
    }
    
    pub async fn cleanup_old_data(&self, days_to_keep: u32) -> Result<(), UsageTrackerError> {
        let cutoff_date = Utc::now() - chrono::Duration::days(days_to_keep as i64);
        let cutoff_str = cutoff_date.format("%Y-%m-%d").to_string();
        
        // Clean up raw data
        let raw_dir = self.data_dir.join("raw");
        if let Ok(entries) = fs::read_dir(&raw_dir) {
            for entry in entries.flatten() {
                if let Some(filename) = entry.file_name().to_str() {
                    if filename.ends_with(".jsonl") {
                        let date_part = &filename[..filename.len() - 6]; // Remove .jsonl
                        if date_part < cutoff_str.as_str() {
                            if let Err(e) = fs::remove_file(entry.path()) {
                                warn!("Failed to remove old raw data file {}: {}", filename, e);
                            } else {
                                info!("Cleaned up old raw data: {}", filename);
                            }
                        }
                    }
                }
            }
        }
        
        // Clean up reports
        let reports_dir = self.data_dir.join("reports");
        if let Ok(entries) = fs::read_dir(&reports_dir) {
            for entry in entries.flatten() {
                if let Some(filename) = entry.file_name().to_str() {
                    if filename.ends_with(".json") {
                        let date_part = &filename[..filename.len() - 5]; // Remove .json
                        if date_part < cutoff_str.as_str() {
                            if let Err(e) = fs::remove_file(entry.path()) {
                                warn!("Failed to remove old report file {}: {}", filename, e);
                            } else {
                                info!("Cleaned up old report: {}", filename);
                            }
                        }
                    }
                }
            }
        }
        
        Ok(())
    }
}
