#!/usr/bin/env python3

import os
import subprocess
import sys
from pathlib import Path

# Configuration
NDK_PATH = r"D:\android-ndk-r27c"
UPX_PATH = r"D:\upx\upx.exe"
TARGET = "aarch64-linux-android"
API_LEVEL = "21"

def run_command(cmd, env=None):
    """Run command and return success status"""
    print(f"Running: {' '.join(cmd)}")
    result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='replace', env=env)

    if result.returncode != 0:
        print(f"Error: {result.stderr}")
        return False

    if result.stdout:
        print(result.stdout)

    return True

def setup_cargo_config():
    """Setup .cargo/config.toml for Android compilation"""
    cargo_dir = Path(".cargo")
    cargo_dir.mkdir(exist_ok=True)

    ndk_bin = Path(NDK_PATH) / "toolchains" / "llvm" / "prebuilt" / "windows-x86_64" / "bin"
    clang_path = ndk_bin / f"{TARGET}{API_LEVEL}-clang.cmd"

    if not clang_path.exists():
        print(f"Error: NDK clang not found at {clang_path}")
        return False

    clang_path_str = str(clang_path).replace('\\', '/')

    config_content = f"""[target.{TARGET}]
linker = '{clang_path_str}'
"""

    config_file = cargo_dir / "config.toml"
    with open(config_file, "w") as f:
        f.write(config_content)

    print(f"Created cargo config with linker: {clang_path}")
    return True

def build_project():
    """Build the project for Android"""
    # Add target if not already added
    run_command(["rustup", "target", "add", TARGET])

    # Set environment variables
    env = os.environ.copy()
    ndk_bin = Path(NDK_PATH) / "toolchains" / "llvm" / "prebuilt" / "windows-x86_64" / "bin"
    sysroot = Path(NDK_PATH) / "toolchains" / "llvm" / "prebuilt" / "windows-x86_64" / "sysroot"

    env[f"CC_{TARGET}"] = str(ndk_bin / f"{TARGET}{API_LEVEL}-clang.cmd")
    env[f"CXX_{TARGET}"] = str(ndk_bin / f"{TARGET}{API_LEVEL}-clang++.cmd")
    env[f"CFLAGS_{TARGET}"] = f"--target={TARGET}{API_LEVEL} --sysroot={sysroot} -fuse-ld=lld"
    env[f"CXXFLAGS_{TARGET}"] = env[f"CFLAGS_{TARGET}"]

    target_upper = TARGET.upper().replace('-', '_')
    env[f"CARGO_TARGET_{target_upper}_LINKER"] = str(ndk_bin / f"{TARGET}{API_LEVEL}-clang.cmd")

    # Build
    cmd = ["cargo", "build", "--target", TARGET, "--release"]
    return run_command(cmd, env)

def compress_binary():
    """Compress the binary with UPX"""
    binary_path = Path("target") / TARGET / "release" / "activity-chronos"

    if not binary_path.exists():
        print(f"Error: Binary not found at {binary_path}")
        return False

    if not Path(UPX_PATH).exists():
        print(f"Error: UPX not found at {UPX_PATH}")
        return False

    return run_command([UPX_PATH, "--lzma", str(binary_path)])

def main():
    """Main build process"""
    print("Building ActivityChronos for Android...")

    if not Path("Cargo.toml").exists():
        print("Error: Cargo.toml not found. Run this script in the project root.")
        sys.exit(1)

    # Setup, build, and compress
    if not setup_cargo_config():
        sys.exit(1)

    if not build_project():
        print("Build failed")
        sys.exit(1)

    if not compress_binary():
        print("Compression failed")
        sys.exit(1)

    # Show results
    binary_path = Path("target") / TARGET / "release" / "activity-chronos"
    print(f"\n✅ Build completed successfully!")
    print(f"📁 Binary: {binary_path.absolute()}")
    print(f"📏 Size: {binary_path.stat().st_size / 1024:.1f} KB")

if __name__ == "__main__":
    main()
