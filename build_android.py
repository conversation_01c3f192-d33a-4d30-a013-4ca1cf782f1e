#!/usr/bin/env python3

import os
import subprocess
import sys
from pathlib import Path

# Configuration
NDK_PATH = r"D:\android-ndk-r27c"
LLVM_PATH = r"D:\LLVM"
UPX_PATH = r"D:\upx\upx.exe"
TARGET = "aarch64-linux-android"
API_LEVEL = "21"  # Minimum API level for arm64

def run_command(cmd, cwd=None):
    """Run command and return success status"""
    print(f"Running: {' '.join(cmd)}")
    result = subprocess.run(cmd, cwd=cwd, capture_output=True, text=True, encoding='utf-8', errors='replace')
    
    if result.returncode != 0:
        print(f"Error: {result.stderr}")
        return False
    
    if result.stdout:
        print(result.stdout)
    
    return True

def setup_cargo_config():
    """Setup .cargo/config.toml for Android compilation"""
    cargo_dir = Path(".cargo")
    cargo_dir.mkdir(exist_ok=True)
    
    # Use NDK's clang for better compatibility
    ndk_bin = Path(NDK_PATH) / "toolchains" / "llvm" / "prebuilt" / "windows-x86_64" / "bin"
    clang_path = ndk_bin / f"{TARGET}{API_LEVEL}-clang.exe"
    
    if not clang_path.exists():
        print(f"Error: NDK clang not found at {clang_path}")
        return False
    
    # NDK sysroot for headers and libraries
    ndk_base = Path(NDK_PATH) / "toolchains" / "llvm" / "prebuilt"
    possible_hosts = ["windows-x86_64", "windows"]
    host_dir = None
    for host in possible_hosts:
        if (ndk_base / host).exists():
            host_dir = host
            break
    
    if not host_dir:
        print(f"Error: No NDK host directory found in {ndk_base}")
        return False
    
    sysroot = ndk_base / host_dir / "sysroot"
    if not sysroot.exists():
        print(f"Error: NDK sysroot not found at {sysroot}")
        return False
    
    # Convert Windows paths to forward slashes for TOML
    clang_path_str = str(clang_path).replace('\\', '/')
    sysroot_str = str(sysroot).replace('\\', '/')
    
    config_content = f"""[target.{TARGET}]
linker = '{clang_path_str}'
"""
    
    config_file = cargo_dir / "config.toml"
    with open(config_file, "w") as f:
        f.write(config_content)
    
    print(f"Created {config_file} with LLVM clang: {clang_path}")
    print(f"Using NDK sysroot: {sysroot}")
    return True

def add_target():
    """Add Android target to Rust"""
    return run_command(["rustup", "target", "add", TARGET])

def build_project():
    """Build the project for Android"""
    # Set environment variables for Android compilation
    env = os.environ.copy()
    
    # NDK paths
    ndk_base = Path(NDK_PATH) / "toolchains" / "llvm" / "prebuilt" / "windows-x86_64"
    sysroot = ndk_base / "sysroot"
    
    # Set CC for the target
    ndk_bin = Path(NDK_PATH) / "toolchains" / "llvm" / "prebuilt" / "windows-x86_64" / "bin"
    env[f"CC_{TARGET}"] = str(ndk_bin / f"{TARGET}{API_LEVEL}-clang.exe")
    env[f"CXX_{TARGET}"] = str(ndk_bin / f"{TARGET}{API_LEVEL}-clang++.exe")
    
    # Set additional flags
    cflags = f"--target={TARGET}{API_LEVEL} --sysroot={sysroot} -fuse-ld=lld"
    env[f"CFLAGS_{TARGET}"] = cflags
    env[f"CXXFLAGS_{TARGET}"] = cflags
    
    # Set CARGO_TARGET environment variables for linking
    target_upper = TARGET.upper().replace('-', '_')
    env[f"CARGO_TARGET_{target_upper}_LINKER"] = str(ndk_bin / f"{TARGET}{API_LEVEL}-clang.exe")
    
    print(f"CC_{TARGET} = {env[f'CC_{TARGET}']}")
    print(f"CFLAGS_{TARGET} = {env[f'CFLAGS_{TARGET}']}")
    
    cmd = ["cargo", "build", "--target", TARGET, "--release"]
    print(f"Running: {' '.join(cmd)}")
    result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='replace', env=env)
    
    if result.returncode != 0:
        print(f"Error: {result.stderr}")
        return False
    
    if result.stdout:
        print(result.stdout)
    
    return True

def compress_binary():
    """Compress the binary with UPX"""
    binary_path = Path("target") / TARGET / "release" / "activity-chronos"
    
    if not binary_path.exists():
        print(f"Error: Binary not found at {binary_path}")
        return False
    
    if not Path(UPX_PATH).exists():
        print(f"Error: UPX not found at {UPX_PATH}")
        return False
    
    return run_command([UPX_PATH, "--lzma", str(binary_path)])

def main():
    """Main build process"""
    print("Building ActivityChronos for Android...")
    
    # Check if we're in the right directory
    if not Path("Cargo.toml").exists():
        print("Error: Cargo.toml not found. Run this script in the project root.")
        sys.exit(1)
    
    # Setup cargo config
    if not setup_cargo_config():
        sys.exit(1)
    
    # Add Android target
    if not add_target():
        print("Failed to add Android target")
        sys.exit(1)
    
    # Build project
    if not build_project():
        print("Build failed")
        sys.exit(1)
    
    # Compress binary
    if not compress_binary():
        print("Compression failed")
        sys.exit(1)
    
    binary_path = Path("target") / TARGET / "release" / "activity-chronos"
    print(f"\nBuild completed successfully!")
    print(f"Binary location: {binary_path.absolute()}")
    print(f"Binary size: {binary_path.stat().st_size / 1024:.1f} KB")

if __name__ == "__main__":
    main()
