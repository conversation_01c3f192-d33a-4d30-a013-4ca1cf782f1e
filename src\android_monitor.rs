use std::process::Command;
use regex::Regex;
use thiserror::Error;
use log::{debug, error};

#[derive(Debug, Error)]
pub enum AndroidMonitorError {
    #[error("ADB command failed: {0}")]
    AdbError(String),
    #[error("Failed to parse activity info: {0}")]
    ParseError(String),
    #[error("No foreground activity found")]
    NoActivityFound,
    #[error("Device not connected: {0}")]
    DeviceNotConnected(String),
}

pub struct AndroidMonitor {
    device_id: Option<String>,
    activity_regex: Regex,
}

impl AndroidMonitor {
    pub async fn new(device_id: Option<String>) -> Result<Self, AndroidMonitorError> {
        // Regex to parse activity manager output
        let activity_regex = Regex::new(r"mCurrentFocus=Window\{[^}]+\s+([^/\s]+)/([^}\s]+)\}")
            .map_err(|e| AndroidMonitorError::ParseError(e.to_string()))?;
        
        let monitor = Self {
            device_id,
            activity_regex,
        };
        
        // Test ADB connection
        monitor.test_adb_connection().await?;
        
        Ok(monitor)
    }
    
    async fn test_adb_connection(&self) -> Result<(), AndroidMonitorError> {
        let mut cmd = Command::new("adb");
        
        if let Some(device) = &self.device_id {
            cmd.args(["-s", device]);
        }
        
        cmd.args(["shell", "echo", "test"]);
        
        let output = cmd.output()
            .map_err(|e| AndroidMonitorError::AdbError(format!("Failed to execute adb: {}", e)))?;
        
        if !output.status.success() {
            let error_msg = String::from_utf8_lossy(&output.stderr);
            return Err(AndroidMonitorError::DeviceNotConnected(error_msg.to_string()));
        }
        
        Ok(())
    }
    
    pub async fn get_foreground_activity(&self) -> Result<String, AndroidMonitorError> {
        // Method 1: Try dumpsys window windows
        if let Ok(activity) = self.get_activity_via_dumpsys_window().await {
            return Ok(activity);
        }
        
        // Method 2: Try dumpsys activity activities
        if let Ok(activity) = self.get_activity_via_dumpsys_activity().await {
            return Ok(activity);
        }
        
        // Method 3: Try dumpsys activity top
        if let Ok(activity) = self.get_activity_via_dumpsys_top().await {
            return Ok(activity);
        }
        
        Err(AndroidMonitorError::NoActivityFound)
    }
    
    async fn get_activity_via_dumpsys_window(&self) -> Result<String, AndroidMonitorError> {
        let output = self.execute_adb_shell_with_grep("dumpsys window windows", "mCurrentFocus").await?;
        
        debug!("dumpsys window output: {}", output);
        
        if let Some(captures) = self.activity_regex.captures(&output) {
            let package = captures.get(1).unwrap().as_str();
            let activity = captures.get(2).unwrap().as_str();
            return Ok(format!("{}/{}", package, activity));
        }
        
        Err(AndroidMonitorError::ParseError("Failed to parse dumpsys window output".to_string()))
    }
    
    async fn get_activity_via_dumpsys_activity(&self) -> Result<String, AndroidMonitorError> {
        let output = self.execute_adb_shell_with_grep("dumpsys activity activities", "mResumedActivity").await?;
        
        debug!("dumpsys activity output: {}", output);
        
        // Parse mResumedActivity format
        let resumed_regex = Regex::new(r"mResumedActivity.*ActivityRecord\{[^}]+\s+([^/\s]+)/([^}\s]+)")
            .map_err(|e| AndroidMonitorError::ParseError(e.to_string()))?;
        
        if let Some(captures) = resumed_regex.captures(&output) {
            let package = captures.get(1).unwrap().as_str();
            let activity = captures.get(2).unwrap().as_str();
            return Ok(format!("{}/{}", package, activity));
        }
        
        Err(AndroidMonitorError::ParseError("Failed to parse dumpsys activity output".to_string()))
    }
    
    async fn get_activity_via_dumpsys_top(&self) -> Result<String, AndroidMonitorError> {
        let output = self.execute_adb_command(&["shell", "dumpsys", "activity", "top"]).await?;
        
        debug!("dumpsys activity top output: {}", output);
        
        // Look for ACTIVITY lines
        let lines: Vec<&str> = output.lines().collect();
        for line in lines {
            if line.trim().starts_with("ACTIVITY") {
                // Parse ACTIVITY line format: ACTIVITY package/activity hash pid
                let parts: Vec<&str> = line.split_whitespace().collect();
                if parts.len() >= 2 {
                    return Ok(parts[1].to_string());
                }
            }
        }
        
        Err(AndroidMonitorError::ParseError("Failed to parse dumpsys activity top output".to_string()))
    }
    
    async fn execute_adb_command(&self, args: &[&str]) -> Result<String, AndroidMonitorError> {
        let mut cmd = Command::new("adb");
        
        if let Some(device) = &self.device_id {
            cmd.args(["-s", device]);
        }
        
        cmd.args(args);
        
        let output = cmd.output()
            .map_err(|e| AndroidMonitorError::AdbError(format!("Failed to execute adb command: {}", e)))?;
        
        if !output.status.success() {
            let error_msg = String::from_utf8_lossy(&output.stderr);
            return Err(AndroidMonitorError::AdbError(error_msg.to_string()));
        }
        
        Ok(String::from_utf8_lossy(&output.stdout).to_string())
    }
    
    async fn execute_adb_shell_with_grep(&self, shell_cmd: &str, grep_pattern: &str) -> Result<String, AndroidMonitorError> {
        let output = self.execute_adb_command(&["shell", shell_cmd]).await?;
        
        // Filter output by grep pattern (cross-platform solution)
        let filtered_lines: Vec<&str> = output
            .lines()
            .filter(|line| line.contains(grep_pattern))
            .collect();
        
        Ok(filtered_lines.join("\n"))
    }
    
    pub async fn get_installed_packages(&self) -> Result<Vec<String>, AndroidMonitorError> {
        let output = self.execute_adb_command(&["shell", "pm", "list", "packages"]).await?;
        
        let packages: Vec<String> = output
            .lines()
            .filter_map(|line| {
                if line.starts_with("package:") {
                    Some(line[8..].to_string())
                } else {
                    None
                }
            })
            .collect();
        
        Ok(packages)
    }
}
