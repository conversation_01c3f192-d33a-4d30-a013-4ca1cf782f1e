use serde::{Deserialize, Serialize};
use std::fs;
use std::path::Path;

#[derive(<PERSON>bug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Config {
    pub monitoring: MonitoringConfig,
    pub storage: StorageConfig,
    pub android: AndroidConfig,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct MonitoringConfig {
    /// Monitoring interval in seconds
    pub interval_seconds: u64,
    /// Minimum duration to record (in seconds)
    pub min_duration_seconds: u64,
    /// Activities to ignore (regex patterns)
    pub ignored_activities: Vec<String>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct StorageConfig {
    /// Number of days to keep raw data
    pub retention_days: u32,
    /// Auto cleanup interval in hours
    pub cleanup_interval_hours: u64,
    /// Enable data compression
    pub enable_compression: bool,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct AndroidConfig {
    /// ADB device ID
    pub device_id: Option<String>,
    /// ADB connection timeout in seconds
    pub adb_timeout_seconds: u64,
    /// Retry attempts for ADB commands
    pub adb_retry_attempts: u32,
}

impl Default for Config {
    fn default() -> Self {
        Self {
            monitoring: MonitoringConfig {
                interval_seconds: 1,
                min_duration_seconds: 1,
                ignored_activities: vec![
                    r"com\.android\.systemui/.*".to_string(),
                    r"com\.android\.launcher/.*".to_string(),
                ],
            },
            storage: StorageConfig {
                retention_days: 30,
                cleanup_interval_hours: 24,
                enable_compression: false,
            },
            android: AndroidConfig {
                device_id: None,
                adb_timeout_seconds: 10,
                adb_retry_attempts: 3,
            },
        }
    }
}

impl Config {
    pub fn load<P: AsRef<Path>>(path: P) -> Result<Self, Box<dyn std::error::Error>> {
        let content = fs::read_to_string(path)?;
        let config: Config = serde_json::from_str(&content)?;
        Ok(config)
    }
    
    pub fn save<P: AsRef<Path>>(&self, path: P) -> Result<(), Box<dyn std::error::Error>> {
        let content = serde_json::to_string_pretty(self)?;
        fs::write(path, content)?;
        Ok(())
    }
    
    pub fn create_default<P: AsRef<Path>>(path: P) -> Result<Config, Box<dyn std::error::Error>> {
        let config = Config::default();
        config.save(path)?;
        Ok(config)
    }
}
