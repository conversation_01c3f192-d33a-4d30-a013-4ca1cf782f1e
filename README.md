# ActivityChronos

一个用于识别安卓前台应用activity名称并记录使用时间的Rust程序。

## 功能特性

- 🔍 **实时监控**: 监控安卓设备前台应用的Activity
- ⏱️ **时间记录**: 准确记录每个Activity的使用时间
- 📊 **数据存储**: 以JSON格式存储原始数据和日报告
- 🔧 **灵活配置**: 支持自定义监控间隔、忽略规则等
- 🧹 **自动清理**: 自动清理过期数据
- 📱 **多设备支持**: 支持指定特定ADB设备

## 前置要求

- Rust 1.70+
- Android SDK (ADB工具)
- 安卓设备已启用USB调试或通过网络连接

## 安装

1. 克隆或下载代码
2. 编译项目:
```bash
cargo build --release
```

## 使用方法

### 基本使用

```bash
# 使用默认配置启动监控
cargo run

# 或使用编译后的二进制文件
./target/release/activity-chronos
```

### 命令行参数

```bash
# 指定配置文件
cargo run -- --config custom-config.json

# 指定数据输出目录
cargo run -- --output-dir ./my-data

# 设置监控间隔（秒）
cargo run -- --interval 2

# 指定ADB设备ID
cargo run -- --device emulator-5554
```

### 配置文件

程序使用JSON配置文件，默认为 `config.json`:

```json
{
  "monitoring": {
    "interval_seconds": 1,
    "min_duration_seconds": 2,
    "ignored_activities": [
      "com\\.android\\.systemui/.*",
      "com\\.android\\.launcher/.*"
    ]
  },
  "storage": {
    "retention_days": 30,
    "cleanup_interval_hours": 24,
    "enable_compression": false
  },
  "android": {
    "device_id": null,
    "adb_timeout_seconds": 10,
    "adb_retry_attempts": 3
  }
}
```

## 数据格式

### 原始数据 (data/raw/YYYY-MM-DD.jsonl)

每行一个JSON对象:

```json
{
  "activity_name": "com.example.app/.MainActivity",
  "package_name": "com.example.app",
  "start_time": "2024-01-01T10:00:00Z",
  "end_time": "2024-01-01T10:05:30Z",
  "duration_seconds": 330
}
```

### 日报告 (data/reports/YYYY-MM-DD.json)

```json
{
  "date": "2024-01-01",
  "total_usage_seconds": 7200,
  "activities": [
    {
      "activity_name": "com.example.app/.MainActivity",
      "package_name": "com.example.app",
      "total_duration_seconds": 1800,
      "session_count": 5,
      "first_use": "2024-01-01T09:00:00Z",
      "last_use": "2024-01-01T18:30:00Z"
    }
  ]
}
```

## 架构设计

程序采用模块化设计:

- `android_monitor`: 负责通过ADB获取前台Activity信息
- `usage_tracker`: 处理使用时间记录和数据存储
- `models`: 定义数据结构
- `config`: 配置管理

### 检测方法

程序使用多种ADB命令获取前台Activity:

1. `dumpsys window windows` - 获取当前焦点窗口
2. `dumpsys activity activities` - 获取活动的Activity
3. `dumpsys activity top` - 获取顶层Activity

## 开发

### 项目结构

```
├── src/
│   ├── main.rs              # 主程序入口
│   ├── android_monitor.rs   # 安卓监控模块
│   ├── usage_tracker.rs     # 使用时间跟踪
│   ├── models.rs           # 数据模型
│   └── config.rs           # 配置管理
├── Cargo.toml              # 项目配置
├── config.json             # 示例配置
└── README.md               # 说明文档
```

### 运行测试

```bash
cargo test
```

### 代码格式化

```bash
cargo fmt
```

## 注意事项

1. **权限要求**: 确保安卓设备已启用USB调试
2. **设备连接**: 程序启动前确认 `adb devices` 能看到设备
3. **性能影响**: 监控间隔不宜过短，建议1-2秒
4. **数据清理**: 定期清理旧数据避免占用过多磁盘空间

## 故障排除

### ADB连接问题

```bash
# 检查设备连接
adb devices

# 重启ADB服务
adb kill-server
adb start-server
```

### 权限问题

确保安卓设备:
- 已启用开发者选项
- 已启用USB调试
- 信任计算机的RSA密钥

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request!
