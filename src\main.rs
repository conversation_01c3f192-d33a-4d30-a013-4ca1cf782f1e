use std::time::{Duration, Instant};
use tokio::time::sleep;
use log::{info, warn, error};
use clap::Parser;

mod android_monitor;
mod usage_tracker;
mod models;
mod config;

use android_monitor::AndroidMonitor;
use usage_tracker::UsageTracker;
use config::Config;

#[derive(Parser)]
#[clap(name = "ActivityChronos")]
#[clap(about = "Monitor Android foreground activities and track usage time")]
struct Cli {
    /// Configuration file path
    #[clap(short, long, default_value = "config.json")]
    config: String,
    
    /// Data output directory
    #[clap(short, long, default_value = "./data")]
    output_dir: String,
    
    /// Monitoring interval in seconds
    #[clap(short, long, default_value = "1")]
    interval: u64,
    
    /// ADB device ID (for desktop monitoring)
    #[clap(short, long)]
    device: Option<String>,
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize logger
    env_logger::init();
    
    let cli = Cli::parse();
    
    info!("Starting ActivityChronos v{}", env!("CARGO_PKG_VERSION"));
    
    // Load configuration
    let _config = Config::load(&cli.config).unwrap_or_else(|_| {
        warn!("Failed to load config, using defaults");
        Config::default()
    });
    
    // Initialize components
    let monitor = AndroidMonitor::new(cli.device.clone()).await?;
    let mut tracker = UsageTracker::new(&cli.output_dir)?;
    
    let interval = Duration::from_secs(cli.interval);
    let mut last_activity = String::new();
    let mut activity_start_time = Instant::now();
    
    info!("Starting monitoring loop with {}s interval", cli.interval);
    
    loop {
        match monitor.get_foreground_activity().await {
            Ok(current_activity) => {
                if current_activity != last_activity {
                    // Record previous activity usage time
                    if !last_activity.is_empty() {
                        let duration = activity_start_time.elapsed();
                        tracker.record_usage(&last_activity, duration).await?;
                        info!("Recorded: {} used for {:?}", last_activity, duration);
                    }
                    
                    // Start tracking new activity
                    last_activity = current_activity.clone();
                    activity_start_time = Instant::now();
                    info!("Current activity: {}", current_activity);
                }
            }
            Err(e) => {
                error!("Failed to get foreground activity: {}", e);
            }
        }
        
        sleep(interval).await;
    }
}
