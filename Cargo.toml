[package]
name = "activity-chronos"
version = "0.1.0"
edition = "2021"

[dependencies]
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
chrono = { version = "0.4", features = ["serde"] }
thiserror = "1.0"
clap = { version = "4.0", features = ["derive"] }
log = "0.4"
env_logger = "0.10"
regex = "1.0"

[target.'cfg(target_os = "android")'.dependencies]
android_logger = "0.13"
jni = "0.21"

[target.'cfg(unix)'.dependencies]
# For ADB communication when running on Unix systems
adb_client = "0.8"
