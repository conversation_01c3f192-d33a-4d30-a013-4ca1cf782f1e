#!/usr/bin/env python3

import subprocess
import sys
from pathlib import Path

def test_binary():
    """Test the compiled Android binary"""
    binary_path = Path("target/aarch64-linux-android/release/activity-chronos")
    
    if not binary_path.exists():
        print(f"❌ Binary not found at {binary_path}")
        return False
    
    print(f"✅ Binary found at {binary_path}")
    print(f"📏 Binary size: {binary_path.stat().st_size / 1024:.1f} KB")
    
    # Check if it's an ARM64 binary (we can't run it on x86_64 Windows)
    try:
        # Try to get file info using PowerShell
        result = subprocess.run([
            "powershell", "-Command", 
            f"Get-ItemProperty '{binary_path}' | Select-Object Length, LastWriteTime"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("📊 Binary properties:")
            print(result.stdout.strip())
        
        print("✅ Binary compilation successful!")
        print("📱 This is an ARM64 Android binary - it can only run on Android devices")
        
        return True
        
    except Exception as e:
        print(f"⚠️  Could not analyze binary: {e}")
        return True  # Still consider it successful since the file exists

def main():
    print("🔍 Testing ActivityChronos Android Binary")
    print("=" * 50)
    
    if test_binary():
        print("\n🎉 Build test completed successfully!")
        print("\n📋 Next steps:")
        print("1. Transfer the binary to an Android device")
        print("2. Make it executable: chmod +x activity-chronos")
        print("3. Run it with appropriate permissions")
        print("\n📁 Binary location:")
        print(f"   {Path('target/aarch64-linux-android/release/activity-chronos').absolute()}")
    else:
        print("\n❌ Build test failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
