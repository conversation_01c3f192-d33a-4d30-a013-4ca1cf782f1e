use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use std::time::Duration;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ActivityUsage {
    pub activity_name: String,
    pub package_name: String,
    pub start_time: DateTime<Utc>,
    pub end_time: DateTime<Utc>,
    pub duration_seconds: u64,
}

impl ActivityUsage {
    pub fn new(activity_name: String, duration: Duration) -> Self {
        let end_time = Utc::now();
        let start_time = end_time - chrono::Duration::from_std(duration).unwrap_or_default();
        
        // Extract package name from activity name (e.g., "com.example.app/.MainActivity" -> "com.example.app")
        let package_name = activity_name
            .split('/')
            .next()
            .unwrap_or(&activity_name)
            .to_string();
        
        Self {
            activity_name,
            package_name,
            start_time,
            end_time,
            duration_seconds: duration.as_secs(),
        }
    }
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct DailyReport {
    pub date: String,
    pub total_usage_seconds: u64,
    pub activities: Vec<ActivitySummary>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ActivitySummary {
    pub activity_name: String,
    pub package_name: String,
    pub total_duration_seconds: u64,
    pub session_count: u32,
    pub first_use: DateTime<Utc>,
    pub last_use: DateTime<Utc>,
}

#[derive(Debug, Clone)]
pub struct ForegroundActivity {
    pub package_name: String,
    pub activity_name: String,
    pub full_name: String,
}

impl ForegroundActivity {
    pub fn new(package_name: String, activity_name: String) -> Self {
        let full_name = if activity_name.starts_with(&package_name) {
            activity_name.clone()
        } else {
            format!("{}/{}", package_name, activity_name)
        };
        
        Self {
            package_name,
            activity_name,
            full_name,
        }
    }
    
    pub fn from_full_name(full_name: String) -> Self {
        if let Some(slash_pos) = full_name.find('/') {
            let package_name = full_name[..slash_pos].to_string();
            let activity_name = full_name[slash_pos + 1..].to_string();
            Self::new(package_name, activity_name)
        } else {
            Self {
                package_name: full_name.clone(),
                activity_name: String::new(),
                full_name,
            }
        }
    }
}
