# ActivityChronos 基本使用示例

## 准备工作

1. 确保已安装 Android SDK 并且 `adb` 命令可用
2. 连接安卓设备并启用USB调试
3. 验证设备连接：
```bash
adb devices
```

## 基本使用

### 1. 默认配置运行
```bash
# 使用默认配置，监控间隔1秒，数据保存到 ./data 目录
cargo run

# 或使用编译后的可执行文件
./target/release/activity-chronos
```

### 2. 自定义参数运行
```bash
# 指定监控间隔为2秒
cargo run -- --interval 2

# 指定数据输出目录
cargo run -- --output-dir ./my-data

# 指定设备ID（如果有多个设备）
cargo run -- --device emulator-5554

# 使用自定义配置文件
cargo run -- --config my-config.json
```

### 3. 查看帮助
```bash
cargo run -- --help
```

## 运行示例

启动程序后，你会看到类似如下的输出：

```
[2024-01-01T10:00:00Z INFO  activity_chronos] Starting ActivityChronos v0.1.0
[2024-01-01T10:00:00Z INFO  activity_chronos] Starting monitoring loop with 1s interval
[2024-01-01T10:00:01Z INFO  activity_chronos] Current activity: com.android.launcher/.MainActivity
[2024-01-01T10:00:05Z INFO  activity_chronos] Current activity: com.example.app/.MainActivity
[2024-01-01T10:00:05Z INFO  activity_chronos] Recorded: com.android.launcher/.MainActivity used for 4s
```

## 数据文件结构

程序运行后会在指定目录创建以下结构：

```
data/
├── raw/
│   └── 2024-01-01.jsonl       # 原始使用记录
└── reports/
    └── 2024-01-01.json        # 日使用报告
```

### 原始数据格式 (raw/YYYY-MM-DD.jsonl)
```json
{"activity_name":"com.example.app/.MainActivity","package_name":"com.example.app","start_time":"2024-01-01T10:00:00Z","end_time":"2024-01-01T10:05:30Z","duration_seconds":330}
```

### 日报告格式 (reports/YYYY-MM-DD.json)
```json
{
  "date": "2024-01-01",
  "total_usage_seconds": 7200,
  "activities": [
    {
      "activity_name": "com.example.app/.MainActivity",
      "package_name": "com.example.app",
      "total_duration_seconds": 1800,
      "session_count": 5,
      "first_use": "2024-01-01T09:00:00Z",
      "last_use": "2024-01-01T18:30:00Z"
    }
  ]
}
```

## 常见问题

### 1. ADB 连接失败
- 确保设备已启用USB调试
- 检查USB线缆连接
- 重启ADB服务：`adb kill-server && adb start-server`

### 2. 权限不足
- 确保设备信任了计算机的RSA密钥
- 部分设备需要在开发者选项中启用"USB调试（安全设置）"

### 3. 检测不到Activity
- 某些系统应用可能无法检测
- 确保目标应用正在前台运行
- 检查设备的Android版本兼容性

## 停止程序

使用 `Ctrl+C` 停止程序。程序会完成当前的数据记录后安全退出。
